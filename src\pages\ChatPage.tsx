'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  content: string
  sender: string
  timestamp: number
  color: string
}

const WS_URL = 'ws://localhost:3001'

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [username, setUsername] = useState('')
  const [clientColor, setClientColor] = useState('#4F46E5')
  const [isConnected, setIsConnected] = useState(false)
  const wsRef = useRef<WebSocket | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message])
  }, [])

  const handleWebSocketMessage = useCallback(
    (event: MessageEvent) => {
      const data = JSON.parse(event.data)

      console.log(data)
      switch (data.type) {
        case 'welcome':
          setUsername(data.username)
          setClientColor(data.color)
          break
        case 'message':
          addMessage({
            id: data.id,
            content: data.content,
            sender: data.sender,
            timestamp: data.timestamp,
            color: data.color
          })
          break
        case 'notification':
          addMessage({
            id: Date.now().toString(),
            content: data.content,
            sender: 'system',
            timestamp: Date.now(),
            color: data.color
          })
          break
      }
    },
    [addMessage]
  )

  const connectWebSocket = useCallback(() => {
    const ws = new WebSocket(WS_URL)
    wsRef.current = ws

    ws.onopen = () => {
      setIsConnected(true)
    }

    ws.onclose = () => {
      setIsConnected(false)
    }

    ws.onmessage = handleWebSocketMessage

    return () => {
      ws.close()
    }
  }, [handleWebSocketMessage])

  useEffect(() => {
    const cleanup = connectWebSocket()
    return cleanup
  }, [connectWebSocket])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  const sendMessage = useCallback(() => {
    if (inputValue.trim() && wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(
        JSON.stringify({
          type: 'chat',
          content: inputValue
        })
      )
      setInputValue('')
    }
  }, [inputValue])

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        sendMessage()
      }
    },
    [sendMessage]
  )

  const formatTime = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
  }, [])

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-purple-50 to-green-50">
      <header className="bg-white shadow-sm p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-green-500">
            Real-Time Chat
          </h1>
          <div className="flex items-center gap-2">
            <div className={cn('w-2 h-2 rounded-full', isConnected ? 'bg-green-500' : 'bg-red-500')} />
            <span className="text-sm text-gray-600">{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
        </div>
      </header>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <div
            key={message.id}
            className={cn(
              'flex gap-3 max-w-3xl mx-auto',
              message.sender === username ? 'justify-end' : 'justify-start'
            )}
          >
            {message.sender !== username && message.sender !== 'system' && (
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-white" style={{ backgroundColor: message.color }}>
                  {message.sender[0]}
                </AvatarFallback>
              </Avatar>
            )}

            <div
              className={cn(
                'rounded-lg p-3 shadow-sm max-w-[70%]',
                message.sender === 'system'
                  ? 'bg-gray-100 text-gray-600 mx-auto text-sm'
                  : message.sender === username
                  ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white'
                  : 'bg-white border border-gray-200'
              )}
            >
              {message.sender !== 'system' && message.sender !== username && (
                <div className="text-xs font-medium mb-1" style={{ color: message.color }}>
                  {message.sender}
                </div>
              )}
              <p className="break-words">{message.content}</p>
              <div className="text-xs mt-1 opacity-70">{formatTime(message.timestamp)}</div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="bg-white border-t p-4">
        <div className="max-w-3xl mx-auto flex gap-2">
          <Input
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            className="flex-1 rounded-full px-4 py-5 shadow-sm"
            disabled={!isConnected}
          />
          <Button
            onClick={sendMessage}
            className="rounded-full px-6 py-5 bg-gradient-to-r from-green-500 to-yellow-400 hover:from-green-600 hover:to-yellow-500 shadow-md"
            disabled={!isConnected || !inputValue.trim()}
          >
            Send
          </Button>
        </div>
      </div>
    </div>
  )
}
