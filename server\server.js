import { WebSocketServer, WebSocket } from 'ws'
import { randomUUID } from 'crypto'
import { hslToHex } from './utils/color.js'

const PORT = process.env.PORT || 3001
const clients = new Map()

const wss = new WebSocketServer({ port: PORT })

wss.on('connection', ws => {
  const clientId = randomUUID()
  const hue = Math.floor(Math.random() * 360)
  const color = hslToHex(hue, 70, 60)

  const client = {
    id: clientId,
    socket: ws,
    username: `User${Math.random().toString(36).substring(2, 8)}`,
    color
  }

  clients.set(clientId, client)

  // Send welcome message with client info
  ws.send(
    JSON.stringify({
      type: 'welcome',
      clientId,
      username: client.username,
      color
    })
  )

  // Broadcast new user connection
  broadcast({
    type: 'notification',
    content: `${client.username} has joined the chat`,
    color: '#4B5563'
  })

  ws.on('message', data => {
    const message = JSON.parse(data)

    console.log(message)
    if (message.type === 'chat') {
      const chatMessage = {
        id: randomUUID(),
        content: message.content,
        sender: client.username,
        timestamp: Date.now(),
        color: client.color
      }

      broadcast({
        type: 'message',
        ...chatMessage
      })
    }
  })

  ws.on('close', () => {
    clients.delete(clientId)
    broadcast({
      type: 'notification',
      content: `${client.username} has left the chat`,
      color: '#4B5563'
    })
  })
})

function broadcast(message) {
  const data = JSON.stringify(message)
  for (const [id, client] of clients) {
    if (client.socket.readyState === WebSocket.OPEN) {
      client.socket.send(data)
    }
  }
}

console.log(`WebSocket server running on port ${PORT}`)
