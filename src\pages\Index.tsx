import { MadeWithDyad } from "@/components/made-with-dyad";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-50 to-green-50">
      <div className="text-center max-w-2xl px-4">
        <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-green-500">
          Real-Time Chat
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Experience seamless communication with our WebSocket-powered chat application
        </p>
        <Button
          asChild
          className="rounded-full px-8 py-6 text-lg bg-gradient-to-r from-green-500 to-yellow-400 hover:from-green-600 hover:to-yellow-500 shadow-lg"
        >
          <Link to="/chat">
            Start Chatting →
          </Link>
        </Button>
      </div>
      <MadeWithDyad />
    </div>
  );
};

export default Index;